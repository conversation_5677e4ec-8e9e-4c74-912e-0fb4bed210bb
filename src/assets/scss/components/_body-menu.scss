.body-menu {

  &-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 28px;
    height: 20px;
    background-color: transparent;
    border-radius: 10px;

    &:hover {
      background-color: $pure-white;
    }

    img {
      width: 17px;
      height: 5px;
      object-fit: cover;
    }
  }

  &.__open &-toggle {
    background-color: $aqua-spring;
  }


  &-actions {
    visibility: hidden;
    z-index: -1;
    position: fixed;
    width: 152px;
      box-shadow: 0 4px 10px 0 $shadow-black,
      0 3px 5px 0 $faint-black,
      0 0 0 1px $shadowy-black;
    border-radius: 4px;
    background-color: $white;

    &-full-text {
      width: 175px;
   }
  }

  ul {
    list-style: none;
  }

  &-item {
    padding-inline: 5px;

    &:first-child {
      padding-top: 11px;
    }
    &:last-child {
      padding-bottom: 11px;
    }

    button {
      width: 100%;
      padding: 6px 15px;
      font-size: 16px;
      font-weight: 700;
      text-align: left;
      color: $black;

      &:hover {
        background-color: $green;
        color: $white;
      }
      &[disabled] {
        background-color: transparent;
        color: #9c9c9c;
      }
    }
  }

  &.__open &-actions {
    visibility: visible;
    z-index: 50;
  }
}
