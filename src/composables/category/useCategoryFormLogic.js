import { ref, computed, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useNuxtApp } from "#app";
import { useCategoryStore } from "@/stores/category.js";
import { useUserDataStore } from "@/stores/userData.js";
import { usePreSignedUrlStore } from "@/stores/preSignedUrl.js";
import { useIsinStore } from "@/stores/isin.js";
import { useEditSearchStore } from "@/stores/editSearch.js";
import { useSearchStore } from "@/stores/search.js";
import { useProjectLang } from "@/composables/useProjectLang";
import { useTimeUtils } from "@/composables/useTimeUtils";
import { useImageUpload } from '@/composables/useImageUpload';
import defaultImage from "@/assets/images/default_recipe_image.png";
import { useStore } from "vuex";

export function useCategoryFormLogic(props) {
  const route = useRoute();
  const router = useRouter();
  const categoryStore = useCategoryStore();
  const userDataStore = useUserDataStore();
  const preSignedUrlStore = usePreSignedUrlStore();
  const isinStore = useIsinStore();
  const editSearchStore = useEditSearchStore();
  const { searchContexts, setSearchQuery } = useSearchStore();
  const { $t, $keys } = useNuxtApp();
  const { readyProject } = useProjectLang();
  const { parseDurationString } = useTimeUtils();
  const store = useStore();

  const getSearchQuery = () => searchContexts.value?.detailsPage?.str;
  const hasSearchQuery = computed(() => !!searchContexts.value?.detailsPage?.str?.trim());

  const backToCategoryPath = computed(() => {
    const fromParam = route.query.from;
    const searchParam = route.query.search;
    if (fromParam) {
      return `/edit-cat-group?isin=${fromParam}`;
    }
    if (searchParam) {
      return `/category?search=${encodeURIComponent(searchParam)}`;
    }
    return '/category';
  });

  const backToCategoryLabel = computed(() => {
    const fromParam = route.query.from;
    if (fromParam) {
      return $t('CATEGORY_GROUP.BACK_MESSAGE');
    }
    return $t('CATEGORY.BACK_MESSAGE');
  });

  const navigateBackToCategory = () => {
    const fromParam = route.query.from;
    const searchParam = route.query.search;

    // If navigating back to category page (not category group), reset search state
    if (!fromParam && searchParam) {
      setSearchQuery('', { emitQueryParam: true, context: 'global' });
      router.push('/category');
    } else {
      // For other cases, use the computed path
      router.push(backToCategoryPath.value);
    }
  };

  // Reactive state
  const isLoading = ref(true);
  const categoriesName = ref("");
  const categoriesSlug = ref("");
  const image = ref("");
  const isPublish = ref(false);
  const hasSlugExist = ref(false);
  const isSlugValidating = ref(false);
  const hasChanges = ref(false);
  const isInitializing = ref(true);
  const categoryISIN = ref("");
  const isAbortedCheckingOperationStatus = ref(false);
  const recipeVariantSelectedLanguage = ref("");
  const removeRecipeData = ref({});
  const categoriesState = ref("unpublished");
  const isCategoryIncludeInHero = ref(false);

  const recipeDataForCategories = ref([]);
  const categoryPromotedRecipes = ref([]);
  const searchConfig = ref({});
  const recipeForCategoriesTotal = ref(0);
  const categoryPromotedRecipesTotal = ref(0);
  const fromRecipe = ref(0);
  const sizeRecipe = ref(10);
  const isPageLoading = ref(false);
  const isUpdating = ref(false);

  const searchcopy = ref("");
  const totalPromotedRemovedIsin = ref([]);
  const addedIsins = ref([]);
  const lang = computed(() => store.getters["userData/getDefaultLang"]);

  const selectedCategoryRecipe = ref([]);
  const recipesAfterPageChange = ref([]);
  const recipeMatchesIsinsTagRemove = ref([]);
  const recipesWereAddedInModal = ref(false);

  const isSelectionEnabled = ref(false);
  const selectedProducts = ref([]);
  const selectedProductsAcrossPages = ref([]);
  const selectionOfRecipes = ref([
    {
      isSelected: false,
    },
  ]);
  const categoryVariantDataIndex = ref("");
  const finalSelectedLanguage = ref([]);
  const recipeMatchesIsinsRemove = ref([]);
  const removeRecipeList = ref([]);
  const filteredRecipeIsins = ref([]);

  const recipeVariantList = ref([]);
  const initiallyVariantSupported = ref([]);
  const saveRemovedCategoryVariants = ref([]);
  const recipeVariantLanguageList = ref([]);
  const recipeVariantLanguage = ref("");
  const recipeVariantLanguageIndex = ref(0);
  const variantName = ref("");
  const hasRecipeVariantLanguagePopUp = ref(false);
  const hasRecipeVariantLanguageResult = ref(false);
  const hasDisableSelectLanguageButton = ref(false);
  const isAddVariantCategoryNamePopUp = ref(false);
  const categoryAssociations = ref({});
  const finalAvailableLangs = ref([]);
  const isCategoryAlertIcon = ref(false);
  const selectedDefaultLang = ref([]);
  const newIsin = ref("");

  const iconInfoOutlineDefaultStyle = ref({
    width: "3.5em",
    maxWidth: "16px",
  });

  const CATEGORY_STATE = {
    PUBLISHED: "published",
  };

  const CATEGORY_STATUS = {
    ACTIVE: "active",
    HIDDEN: "hidden",
    PUBLISHED: "published",
  };

  const RECIPE_ACTION_CASE = {
    PREVIEW: "preview",
    REMOVE: "remove",
    PROMOTE: "promote",
    UNPROMOTE: "unpromote",
  };

  const promotedRecipeColumnNames = [
    "",
    "Recipe ISIN",
    "Recipe Title",
    "Total Time",
    "Ingredient Count",
    "",
  ];
  const promotedRecipeColumnKeys = [
    "image",
    "isin",
    "title",
    "totalTime",
    "ingredientCount",
    "actions",
  ];

  const categoryRecipeColumnNames = computed(() => {
    if (isSelectionEnabled.value) {
      return [
        "",
        "",
        "Recipe ISIN",
        "Recipe Title",
        "Total Time",
        "Ingredient Count",
        "",
      ];
    }
    return [
      "",
      "Recipe ISIN",
      "Recipe Title",
      "Total Time",
      "Ingredient Count",
      "",
    ];
  });

  const categoryRecipeColumnKeys = computed(() => {
    if (isSelectionEnabled.value) {
      return [
        "checkbox",
        "image",
        "isin",
        "title",
        "totalTime",
        "ingredientCount",
        "actions",
      ];
    }
    return ["image", "isin", "title", "totalTime", "ingredientCount", "actions"];
  });

  const hintID = computed(() => {
    return props.isEdit && categoryISIN.value
      ? `ISIN: ${categoryISIN.value}`
      : "";
  });

  const isCategoryValid = computed(() => {
    return (
      categoriesName.value.trim() !== "" &&
      image.value !== "" &&
      image.value !== defaultImage
    );
  });

  const continueButtonLabel = computed(() => {
    return categoriesState.value === CATEGORY_STATUS.PUBLISHED ? $t('BUTTONS.PUBLISH_BUTTON') : $t('BUTTONS.SAVE_BUTTON');
  });

  const isContinueButtonEnabled = computed(() => {
    const hasNameOrEmpty = recipeVariantList.value.length === 0 || !!recipeVariantList.value[0]?.name;
    const isSlugReady = !isSlugValidating.value;
    return (props.isEdit ? hasChanges.value : true) && isCategoryValid.value && hasNameOrEmpty && isSlugReady;
  });

  const selectedRecipeCount = ref(0);
  const checkSelectedRecipes = selectedRecipeCount;

  const updateSelectionCount = () => {
    const currentPageSelectedIsins = recipeDataForCategories.value
      .filter((data) => data.isSelectedToDelete === true)
      .map(item => item.isin);
    const otherPagesCount = selectedProductsAcrossPages.value.filter(
      item => !currentPageSelectedIsins.includes(item.isin)
    ).length;
    const totalCount = currentPageSelectedIsins.length + otherPagesCount;
    selectedRecipeCount.value = totalCount;
    return totalCount;
  };

  const {
    uploadFile,
    uploadImagePercentage: imageUploadPercentage,
    loadedImageSize: imageLoadedSize,
    imageResponseUrl
  } = useImageUpload({
    entity: 'category',
    onImageUploaded: (result) => {
      image.value = result;
      if (props.isEdit && !isInitializing.value) {
        hasChanges.value = true;
      }
    },
    getPreSignedUrl: (params) => {
      const isin = categoryISIN.value || newIsin.value;
      if (!isin) {
        console.error("[IQ][CategoryForm] Missing ISIN for pre-signed URL");
        return Promise.reject(new Error("Missing ISIN"));
      }
      return preSignedUrlStore.getPreSignedImageUrlAsync(isin, {
        ...params,
        lang: lang.value
      });
    }
  });

  // Watchers
  watch(
    [categoriesName, categoriesState, categoriesSlug],
    ([newName, newStatus, newSlug], [oldName, oldStatus, oldSlug]) => {
      if (
        props.isEdit &&
        !isInitializing.value &&
        (newName !== oldName || newStatus !== oldStatus || newSlug !== oldSlug)
      ) {
        hasChanges.value = true;
      }
    }
  );

  watch(
    searchContexts,
    (newSearchQuery, oldSearchQuery) => {
      if (newSearchQuery.detailsPage?.str !== oldSearchQuery.detailsPage?.str) {
        searchcopy.value = newSearchQuery?.str || "";
      }
    },
    { immediate: false }
  );

  return {
    // Props
    props,

    // State
    isLoading,
    categoriesName,
    categoriesSlug,
    image,
    isPublish,
    hasSlugExist,
    isSlugValidating,
    hasChanges,
    isInitializing,
    categoryISIN,
    isAbortedCheckingOperationStatus,
    recipeVariantSelectedLanguage,
    removeRecipeData,
    categoriesState,
    isCategoryIncludeInHero,
    recipeDataForCategories,
    categoryPromotedRecipes,
    searchConfig,
    recipeForCategoriesTotal,
    categoryPromotedRecipesTotal,
    fromRecipe,
    sizeRecipe,
    isPageLoading,
    isUpdating,
    searchcopy,
    totalPromotedRemovedIsin,
    addedIsins,
    lang,
    selectedCategoryRecipe,
    recipesAfterPageChange,
    recipeMatchesIsinsTagRemove,
    recipesWereAddedInModal,
    isSelectionEnabled,
    selectedProducts,
    selectedProductsAcrossPages,
    selectionOfRecipes,
    categoryVariantDataIndex,
    finalSelectedLanguage,
    recipeMatchesIsinsRemove,
    removeRecipeList,
    filteredRecipeIsins,
    recipeVariantList,
    initiallyVariantSupported,
    saveRemovedCategoryVariants,
    recipeVariantLanguageList,
    recipeVariantLanguage,
    recipeVariantLanguageIndex,
    variantName,
    hasRecipeVariantLanguagePopUp,
    hasRecipeVariantLanguageResult,
    hasDisableSelectLanguageButton,
    isAddVariantCategoryNamePopUp,
    categoryAssociations,
    finalAvailableLangs,
    isCategoryAlertIcon,
    selectedDefaultLang,
    newIsin,
    iconInfoOutlineDefaultStyle,

    // Constants
    CATEGORY_STATE,
    CATEGORY_STATUS,
    RECIPE_ACTION_CASE,
    promotedRecipeColumnNames,
    promotedRecipeColumnKeys,

    // Computed
    categoryRecipeColumnNames,
    categoryRecipeColumnKeys,
    hintID,
    isCategoryValid,
    continueButtonLabel,
    isContinueButtonEnabled,
    selectedRecipeCount,
    checkSelectedRecipes,

    // Functions
    updateSelectionCount,
    uploadFile,
    imageUploadPercentage,
    imageLoadedSize,
    imageResponseUrl,

    // Utils
    route,
    router,
    categoryStore,
    userDataStore,
    preSignedUrlStore,
    isinStore,
    editSearchStore,
    $t,
    $keys,
    readyProject,
    parseDurationString,
    getSearchQuery,
    hasSearchQuery,
    backToCategoryPath,
    backToCategoryLabel,
    navigateBackToCategory,
  };
}
